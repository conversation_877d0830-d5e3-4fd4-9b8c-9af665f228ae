Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.21f1c1 (08fa194de70f) revision 588313'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 32717 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\2021.3.21f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
G:/GAME_ZD/Brotato_LegionSurvivors
-logFile
Logs/AssetImportWorker7.log
-srvPort
5866
Successfully changed project path to: G:/GAME_ZD/Brotato_LegionSurvivors
G:/GAME_ZD/Brotato_LegionSurvivors
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [47704] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2865508897 [EditorId] 2865508897 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [47704] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2865508897 [EditorId] 2865508897 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 168.24 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.21f1c1 (08fa194de70f)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/GAME_ZD/Brotato_LegionSurvivors/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56272
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003731 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 277 ms
Refreshing native plugins compatible for Editor in 143.31 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.004 seconds
Domain Reload Profiling:
	ReloadAssembly (1004ms)
		BeginReloadAssembly (87ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (818ms)
			LoadAssemblies (84ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (127ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (604ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (339ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (143ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (83ms)
				ProcessInitializeOnLoadMethodAttributes (37ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.010648 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 168.24 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.230 seconds
Domain Reload Profiling:
	ReloadAssembly (1231ms)
		BeginReloadAssembly (113ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (1015ms)
			LoadAssemblies (84ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (630ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (168ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (330ms)
				ProcessInitializeOnLoadMethodAttributes (38ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 2.02 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 130 unused Assets / (134.3 KB). Loaded Objects now: 3664.
Memory consumption went from 152.3 MB to 152.2 MB.
Total: 3.235800 ms (FindLiveObjects: 0.304200 ms CreateObjectMapping: 0.116300 ms MarkObjects: 2.706200 ms  DeleteObjects: 0.108300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011555 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.74 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.068 seconds
Domain Reload Profiling:
	ReloadAssembly (1069ms)
		BeginReloadAssembly (137ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (830ms)
			LoadAssemblies (88ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (194ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (42ms)
			SetupLoadedEditorAssemblies (378ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (62ms)
				ProcessInitializeOnLoadAttributes (244ms)
				ProcessInitializeOnLoadMethodAttributes (46ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (78ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 11.47 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3679.
Memory consumption went from 147.8 MB to 147.7 MB.
Total: 3.507800 ms (FindLiveObjects: 0.363000 ms CreateObjectMapping: 0.116900 ms MarkObjects: 2.961600 ms  DeleteObjects: 0.065300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014700 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.50 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.095 seconds
Domain Reload Profiling:
	ReloadAssembly (1096ms)
		BeginReloadAssembly (134ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (862ms)
			LoadAssemblies (86ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (201ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (388ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (62ms)
				ProcessInitializeOnLoadAttributes (252ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (79ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.05 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.6 KB). Loaded Objects now: 3694.
Memory consumption went from 146.8 MB to 146.7 MB.
Total: 3.613300 ms (FindLiveObjects: 0.325200 ms CreateObjectMapping: 0.108900 ms MarkObjects: 3.119000 ms  DeleteObjects: 0.058700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 188551.365294 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/MenuCatalog.prefab
  artifactKey: Guid(1df7dd29ad4850747b1d2a39e5c6c01f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/MenuCatalog.prefab using Guid(1df7dd29ad4850747b1d2a39e5c6c01f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fb7d15488a865aec09775d4ae90cf204') in 0.079508 seconds 
