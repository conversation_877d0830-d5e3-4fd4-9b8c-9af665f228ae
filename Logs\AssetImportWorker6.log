Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.21f1c1 (08fa194de70f) revision 588313'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 32717 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\2021.3.21f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
G:/GAME_ZD/Brotato_LegionSurvivors
-logFile
Logs/AssetImportWorker6.log
-srvPort
5866
Successfully changed project path to: G:/GAME_ZD/Brotato_LegionSurvivors
G:/GAME_ZD/Brotato_LegionSurvivors
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34172] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2514689513 [EditorId] 2514689513 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [34172] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2514689513 [EditorId] 2514689513 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 196.06 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.21f1c1 (08fa194de70f)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/GAME_ZD/Brotato_LegionSurvivors/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56772
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004367 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 282 ms
Refreshing native plugins compatible for Editor in 163.52 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.033 seconds
Domain Reload Profiling:
	ReloadAssembly (1034ms)
		BeginReloadAssembly (93ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (837ms)
			LoadAssemblies (90ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (115ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (26ms)
			SetupLoadedEditorAssemblies (642ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (348ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (164ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (86ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.012503 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 159.15 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.330 seconds
Domain Reload Profiling:
	ReloadAssembly (1331ms)
		BeginReloadAssembly (118ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (1098ms)
			LoadAssemblies (90ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (224ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (649ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (159ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (344ms)
				ProcessInitializeOnLoadMethodAttributes (46ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 3.40 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 130 unused Assets / (134.3 KB). Loaded Objects now: 3664.
Memory consumption went from 152.4 MB to 152.2 MB.
Total: 4.682900 ms (FindLiveObjects: 0.425800 ms CreateObjectMapping: 0.118000 ms MarkObjects: 3.986800 ms  DeleteObjects: 0.150600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011257 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.63 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.139 seconds
Domain Reload Profiling:
	ReloadAssembly (1139ms)
		BeginReloadAssembly (142ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (883ms)
			LoadAssemblies (93ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (216ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (55ms)
			SetupLoadedEditorAssemblies (394ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (66ms)
				ProcessInitializeOnLoadAttributes (252ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (77ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.93 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3679.
Memory consumption went from 147.8 MB to 147.7 MB.
Total: 4.814800 ms (FindLiveObjects: 0.470500 ms CreateObjectMapping: 0.332200 ms MarkObjects: 3.897500 ms  DeleteObjects: 0.112900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 188431.668124 seconds.
  path: Assets/RawResources/UI/1.png
  artifactKey: Guid(393a4e674a0edbf49a090aa755125e6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/1.png using Guid(393a4e674a0edbf49a090aa755125e6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd20c634752f797fe0d714d30611b89b0') in 0.120099 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawResources/UI/9.png
  artifactKey: Guid(2b9193ca00bad9741b703745d987de7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/9.png using Guid(2b9193ca00bad9741b703745d987de7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3749794a9e615b093ecfeab42c8f3b47') in 0.012561 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawResources/UI/Circle.png
  artifactKey: Guid(72798c93738542eea7f2cb163969c444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/Circle.png using Guid(72798c93738542eea7f2cb163969c444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a26c53f28c30132c798964146a50a59b') in 0.032218 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawResources/UI/8.png
  artifactKey: Guid(d05bc96064fbb4947a200a5864a2101d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/8.png using Guid(d05bc96064fbb4947a200a5864a2101d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '595066eb5310b08a0cee4fa13b47a419') in 0.010429 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/RawResources/UI/3.png
  artifactKey: Guid(98b27318f395f9744a8db88d3db31c93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/3.png using Guid(98b27318f395f9744a8db88d3db31c93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cfea0745e43acf69572dffac69d7225c') in 0.009500 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/RawResources/UI/8+.png
  artifactKey: Guid(b5ab275efe422bb46a593be0ec8c8fb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/8+.png using Guid(b5ab275efe422bb46a593be0ec8c8fb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6fe6424e6a77a0d65b269a2472a2f4b5') in 0.015281 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawResources/UI/4.png
  artifactKey: Guid(8838275e7bbade440884286cfd2d2f64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/4.png using Guid(8838275e7bbade440884286cfd2d2f64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4e68cd461dd0ad47068e3c9088bcdeda') in 0.010429 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/RawResources/UI/6.png
  artifactKey: Guid(1d777e5b53020a04cb6c8f6171354c95) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/UI/6.png using Guid(1d777e5b53020a04cb6c8f6171354c95) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c7d58d5d62da639bca70ef2c1a8b88ce') in 0.010323 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0